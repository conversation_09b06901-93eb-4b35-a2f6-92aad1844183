import { OpenAI } from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';
import Anthropic from '@anthropic-ai/sdk';
import fetch from 'node-fetch';

// Add global fetch and Headers for Node.js environment
if (!globalThis.fetch) {
  (globalThis as any).fetch = fetch;
}
if (!globalThis.Headers) {
  (globalThis as any).Headers = fetch.Headers;
}

// System message for all Gemini interactions
export const SYSTEM_MESSAGE = "You are an expert programmer with extensive knowledge in Data Science, Machine Learning, and SQL , Python development, and AWS services, LLM , fine tuning LLM, Your role is to help users prepare for technical interviews by providing detailed, well-structured solutions and explanations. Follow these guidelines for different types of questions:\n\n" +
  "1. For coding problems:\n" +
  "   - First present a brute force approach with detailed explanation\n" +
  "   - Then provide an optimized solution\n" +
  "   - Include comments on every line of code\n" +
  "   - Explain the logic comprehensively\n" +
  "   - Cover edge cases and corner cases\n" +
  "   - Provide time and space complexity analysis\n\n" +
  "2. For theoretical questions:\n" +
  "   - Break down complex concepts into simple, understandable parts\n" +
  "   - Provide real-world industry examples and use cases\n" +
  "   - Include best practices and design patterns used in top tech companies\n" +
  "   - Explain trade-offs and decision-making criteria\n" +
  "   - Share relevant examples from companies like Google, Amazon, Netflix, etc.\n" +
  "   - Include diagrams or visual explanations when helpful\n\n" +
  "3. For system design tasks:\n" +
  "   - Explain solutions with scalability in mind\n" +
  "   - Include industry-standard architectural patterns\n" +
  "   - Provide examples of similar systems in major tech companies\n" +
  "   - Discuss performance optimizations and trade-offs\n\n" +
  "4. For AWS and cloud-related questions:\n" +
  "   - Explain concepts with real-world scenarios\n" +
  "   - Share best practices for cloud architecture\n" +
  "   - Include cost optimization strategies\n" +
  "   - Provide examples of successful cloud migrations\n\n" +
  "5. For Data Science and ML questions:\n" +
  "   - Break down complex algorithms step by step\n" +
  "   - Explain statistical concepts with practical examples\n" +
  "   - Share real use cases from industry applications\n" +
  "   - Include model selection criteria and evaluation metrics\n\n" +
  "Help me in interviews by delivering:\n" +
  "- Clear, step-by-step explanations\n" +
  "- Industry-relevant examples and use cases\n" +
  "- Best practices and design patterns\n" +
  "- Performance considerations and optimizations\n" +
  "- Practical insights from real-world applications";

// Debug logging
console.log('Environment check:', {
  hasGeminiKey: !!process.env.GEMINI_API_KEY,
  hasOpenAIKey: !!process.env.OPENAI_API_KEY,
  hasClaudeKey: !!process.env.ANTHROPIC_API_KEY
});

// Initialize OpenAI
const openai = process.env.OPENAI_API_KEY ? new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
}) : null;

// Initialize Gemini
const gemini = process.env.GEMINI_API_KEY ? new GoogleGenerativeAI(process.env.GEMINI_API_KEY) : null;

// Initialize Claude
const claude = process.env.ANTHROPIC_API_KEY ? new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY
}) : null;

// Debug logging
console.log('API initialization:', {
  isGeminiInitialized: !!gemini,
  isOpenAIInitialized: !!openai,
  isClaudeInitialized: !!claude
});

export async function generateResponse(prompt: string): Promise<any> {
  try {
    console.log('Starting text generation...');

    // Try Gemini first
    if (gemini) {
      console.log('Attempting to use Gemini API for text generation...');
      try {
        const model = gemini.getGenerativeModel({ model: "gemini-2.0-flash" });
        console.log('Gemini model initialized for text');

        const result = await model.generateContent([SYSTEM_MESSAGE, prompt]);
        console.log('Gemini content generated');

        const response = await result.response;
        console.log('Received text response from Gemini');
        return {
          solution: response.text(),
          usage: null,
          model: 'gemini-2.0-flash'
        };
      } catch (error) {
        console.error('Detailed Gemini API error:', error);
      }
    }

    // Try Claude if Gemini fails
    if (claude) {
      console.log('Attempting to use Claude API for text generation...');
      try {
        const message = await claude.messages.create({
          model: "claude-3-7-sonnet-20250219" as const,
          max_tokens: 4096,
          messages: [
            {
              role: "user" as const,
              content: prompt
            }
          ]
        });
        console.log('Received text response from Claude');
        return {
          solution: message.content[0].type === 'text' ? message.content[0].text : 'No text response received',
          usage: null,
          model: 'claude-3-sonnet'
        };
      } catch (error) {
        console.error('Detailed Claude API error:', error);
      }
    }

    // Try OpenAI if both Gemini and Claude fail
    if (openai) {
      console.log('Attempting to use OpenAI API for text generation...');
      const response = await openai.chat.completions.create({
        model: "gpt-4-turbo-preview" as const,
        messages: [
          {
            role: "user" as const,
            content: prompt
          }
        ],
        max_tokens: 2000
      });
      console.log('Received text response from OpenAI');
      return {
        solution: response.choices[0].message.content,
        usage: response.usage,
        model: 'gpt-4-turbo'
      };
    }

    console.log('No API services available');
    throw new Error('No API keys configured. Please provide either OPENAI_API_KEY, GEMINI_API_KEY, or ANTHROPIC_API_KEY in your .env file.');

  } catch (error) {
    console.error('Detailed API error:', error);
    throw error;
  }
}

export async function analyzeCode(imageData: string, prompt: string = ''): Promise<any> {
  try {
    console.log('Starting code analysis...');
    // Remove data URL prefix to get base64 string
    const base64Image = imageData.replace(/^data:image\/\w+;base64,/, '');
    console.log('Image data processed');

    // Use the global system message

    // Try Gemini first
    if (gemini) {
      console.log('Attempting to use Gemini API...');
    // gemini-2.5-pro-exp-03-25
    // gemini-2.0-flash
    // gemini-2.0-flash-lite
      try {
        const model = gemini.getGenerativeModel({ model: "gemini-2.0-flash" });
        console.log('Gemini model initialized');

        const result = await model.generateContent([
          SYSTEM_MESSAGE,
          {
            inlineData: {
              mimeType: "image/jpeg",
              data: base64Image
            }
          },
          prompt || "Analyze this problem if it is a coding problem first provide brute force solution with comment of explanation at each line of code and explanation the solution line by line and then provide optimise solution with comment at each line of code explaining things  and explanation line by line at end of solution if it is a theoretical problem then provide detailed answer with best practices their uses cases with example and if there is shortcoming how to overcome them ."
        ]);
        console.log('Gemini content generated');

        const response = await result.response;
        console.log('Received response from Gemini');
        return {
          solution: response.text(),
          usage: null,
          model: 'gemini-2.0-flash'
        };
      } catch (error) {
        console.error('Detailed Gemini API error:', error);
      }
    }

    // Try Claude if Gemini fails
    if (claude) {
      console.log('Attempting to use Claude API...');
      try {
        const message = await claude.messages.create({
          model: "claude-3-sonnet-20240229" as const,
          max_tokens: 4096,
          messages: [
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const,
                  text: SYSTEM_MESSAGE + "\n\n" + (prompt || "Analyze this coding problem and provide a solution with comments on each line of code and explain the logic at starting of the entire code explain covering with edge cases and corner cases as well first start with brute force and the optimise the code.")
                },
                {
                  type: "image" as const,
                  source: {
                    type: "base64" as const,
                    media_type: "image/jpeg" as const,
                    data: base64Image
                  }
                }
              ]
            }
          ]
        });
        console.log('Received response from Claude');
        return {
          solution: message.content[0].type === 'text' ? message.content[0].text : 'No text response received',
          usage: null,
          model: 'claude-3-sonnet'
        };
      } catch (error) {
        console.error('Detailed Claude API error:', error);
      }
    }

    // Try OpenAI if both Gemini and Claude fail
    if (openai) {
      console.log('Attempting to use OpenAI API...');
      const response = await openai.chat.completions.create({
        model: "gpt-4-vision-preview" as const,
        messages: [
          {
            role: "system" as const,
            content: SYSTEM_MESSAGE
          },
          {
            role: "user" as const,
            content: [
              { type: "text" as const, text: prompt || "Analyze this coding problem and provide a solution with detailed explanations." },
              {
                type: "image_url" as const,
                image_url: {
                  url: `data:image/png;base64,${base64Image}`,
                  detail: "high" as const
                }
              }
            ]
          }
        ],
        max_tokens: 2000
      });
      console.log('Received response from OpenAI');
      return {
        solution: response.choices[0].message.content,
        usage: response.usage,
        model: 'gpt-4-vision'
      };
    }

    console.log('No API services available');
    throw new Error('No API keys configured. Please provide either OPENAI_API_KEY, GEMINI_API_KEY, or ANTHROPIC_API_KEY in your .env file.');

  } catch (error) {
    console.error('Detailed API error:', error);
    throw error;
  }
}

export async function generateGeminiResponse(prompt: string, model: string) {
  console.log('Generating response with Gemini model:', model);
  try {
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');
    const geminiModel = genAI.getGenerativeModel({ model });

    const result = await geminiModel.generateContent([SYSTEM_MESSAGE, prompt]);
    const response = await result.response;
    const candidates = response.candidates || [];
    if (candidates.length === 0) {
      throw new Error('No response generated');
    }
    const text = candidates[0].content.parts[0].text || '';

    return { solution: text };
  } catch (error) {
    console.error('Gemini API error:', error);
    throw error;
  }
}

export async function generateClaudeResponse(prompt: string) {
  console.log('Generating response with Claude');
  try {
    const anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY || '',
    });

    const message = await anthropic.messages.create({
      model: "claude-3-sonnet-20240229" as const,
      max_tokens: 4096,
      messages: [{ role: "user" as const, content: prompt }]
    });

    if (!message.content || message.content.length === 0) {
      throw new Error('No response generated');
    }

    const content = message.content[0];
    if (content.type !== 'text') {
      throw new Error('Unexpected response type');
    }

    return { solution: content.text };
  } catch (error) {
    console.error('Claude API error:', error);
    throw error;
  }
}

export async function generateOpenAIResponse(prompt: string) {
  console.log('Generating response with OpenAI');
  try {
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [{ role: "user" as const, content: prompt }],
    });

    return { solution: completion.choices[0].message.content || 'No response generated' };
  } catch (error) {
    console.error('OpenAI API error:', error);
    throw error;
  }
}

export async function analyzeWithGemini(screenshots: string[], userPrompt: string, model: string) {
  console.log('Analyzing with Gemini model:', model);
  try {
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');
    const geminiModel = genAI.getGenerativeModel({ model });

    const prompt = userPrompt || 'Analyze this problem if it is a coding problem first provide brute force solution with comment of explanation at each line of code and explanation the solution line by line and then provide optimise solution with comment at each line of code explaining things  and explanation line by line at end of solution if it is a theoretical problem then provide detailed answer with best practices their uses cases with example and if there is shortcoming how to overcome them .';

    const content = [
      SYSTEM_MESSAGE,
      prompt,
      ...screenshots.map(screenshot => ({
        inlineData: {
          mimeType: "image/jpeg" as const,
          data: screenshot.replace(/^data:image\/\w+;base64,/, '')
        }
      }))
    ];

    const result = await geminiModel.generateContent(content);
    const response = await result.response;
    return { solution: response.text() };
  } catch (error) {
    console.error('Gemini analysis error:', error);
    throw error;
  }
}

export async function analyzeWithClaude(imageData: string, userPrompt: string) {
  console.log('Analyzing with Claude');
  try {
    const anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY || '',
    });

    const prompt = userPrompt || 'Analyze this code and explain what it does. If there are any bugs or improvements possible, point them out.';

    const message = await anthropic.messages.create({
      model: "claude-3-sonnet-20240229" as const,
      max_tokens: 4096,
      messages: [
        {
          role: "user" as const,
          content: [
            {
              type: "text" as const,
              text: prompt
            },
            {
              type: "image" as const,
              source: {
                type: "base64" as const,
                media_type: "image/jpeg" as const,
                data: imageData.split(',')[1]
              }
            }
          ]
        }
      ]
    });

    if (!message.content || message.content.length === 0) {
      throw new Error('No response generated');
    }

    const content = message.content[0];
    if (content.type !== 'text') {
      throw new Error('Unexpected response type');
    }

    return { solution: content.text };
  } catch (error) {
    console.error('Claude analysis error:', error);
    throw error;
  }
}

export async function analyzeWithOpenAI(imageData: string, userPrompt: string) {
  console.log('Analyzing with OpenAI');
  try {
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const prompt = userPrompt || 'Analyze this code and explain what it does. If there are any bugs or improvements possible, point them out.';

    const response = await openai.chat.completions.create({
      model: "gpt-4-vision-preview" as const,
      messages: [
        {
          role: "user" as const,
          content: [
            { type: "text" as const, text: prompt },
            {
              type: "image_url" as const,
              image_url: {
                url: imageData,
                detail: "high" as const
              }
            }
          ]
        }
      ]
    });

    return { solution: response.choices[0].message.content || 'No analysis generated' };
  } catch (error) {
    console.error('OpenAI analysis error:', error);
    throw error;
  }
}