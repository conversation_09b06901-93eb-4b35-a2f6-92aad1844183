import { BrowserWindow, screen } from 'electron';
import path from 'path';
import isDev from 'electron-is-dev';
import { globalShortcut } from 'electron';

export class WindowManager {
  mainWindow: BrowserWindow | null = null;
  isWindowVisible = true;
  
  createMainWindow(): BrowserWindow {
    // Get display dimensions
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    
    // Create the browser window
    this.mainWindow = new BrowserWindow({
      width: 500,
      height: 600,
      x: width - 550, // Position near the edge of the screen
      y: 50,
      frame: false, // Remove window frame
      transparent: true,
      alwaysOnTop: true,
      backgroundColor: '#00000000', // Transparent background
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        preload: path.join(__dirname, '../preload.js')
      },
      // Modified window properties for better interaction
      focusable: true, // Allow window to be focused
      skipTaskbar: true, // Hide from taskbar
      hasShadow: false, // Remove window shadow
      roundedCorners: false, // Remove rounded corners
    });
    
    // Load app from the dist directory
    const startUrl = `file://${path.join(__dirname, '../renderer/index.html')}`;
    
    this.mainWindow.loadURL(startUrl);
    
    // Hide the menu bar
    this.mainWindow.setMenuBarVisibility(false);
    
    // Only ignore mouse events in production and when window is set to be ignored
    if (!isDev) {
      this.setIgnoreMouseEvents(false);
    }
    
    // Additional window settings
    this.mainWindow.setVisibleOnAllWorkspaces(false);
    this.mainWindow.setAlwaysOnTop(true, 'screen-saver');
    this.mainWindow.setContentProtection(true);
    
    // Register keyboard shortcuts
    globalShortcut.register('CommandOrControl+Enter', () => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('generate-analysis');
      }
    });

    globalShortcut.register('CommandOrControl+G', () => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('generate-analysis');
      }
    });

    globalShortcut.register('CommandOrControl+H', () => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('take-screenshot');
      }
    });

    globalShortcut.register('CommandOrControl+R', () => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('reset-app');
      }
    });

    globalShortcut.register('CommandOrControl+B', () => {
      this.toggleVisibility();
    });

    // Add shortcut for Developer Tools
    globalShortcut.register('CommandOrControl+Alt+I', () => {
      this.mainWindow?.webContents.toggleDevTools();
    });
    
    return this.mainWindow;
  }

  setIgnoreMouseEvents(ignore: boolean) {
    if (this.mainWindow) {
      this.mainWindow.setIgnoreMouseEvents(ignore, { forward: true });
    }
  }
  
  moveActiveWindow(direction: string): void {
    if (!this.mainWindow) return;
    
    const [x, y] = this.mainWindow.getPosition();
    const moveStep = 50;
    
    switch (direction) {
      case 'up':
        this.mainWindow.setPosition(x, y - moveStep);
        break;
      case 'down':
        this.mainWindow.setPosition(x, y + moveStep);
        break;
      case 'left':
        this.mainWindow.setPosition(x - moveStep, y);
        break;
      case 'right':
        this.mainWindow.setPosition(x + moveStep, y);
        break;
    }
  }
  
  toggleVisibility(): void {
    if (!this.mainWindow) return;
    
    this.isWindowVisible = !this.isWindowVisible;
    
    if (this.isWindowVisible) {
      this.mainWindow.show();
      this.mainWindow.webContents.send('toggle-visibility', true);
      if (!isDev) {
        this.setIgnoreMouseEvents(false);
      }
    } else {
      this.mainWindow.hide();
      this.mainWindow.webContents.send('toggle-visibility', false);
      if (!isDev) {
        this.setIgnoreMouseEvents(true);
      }
    }
  }
  
  isVisible(): boolean {
    return this.isWindowVisible;
  }
  
  getMainWindow(): BrowserWindow | null {
    return this.mainWindow;
  }
  
  quitApp(): void {
    if (this.mainWindow) {
      this.mainWindow.close();
    }
  }
} 