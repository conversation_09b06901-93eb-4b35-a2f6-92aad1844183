import React from 'react';
import { Prism as Syntax<PERSON><PERSON>lighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface SolutionDisplayProps {
  solution: string;
}

interface ContentPart {
  type: 'text' | 'code';
  content: string;
  language?: string;
}

const SolutionDisplay: React.FC<SolutionDisplayProps> = ({ solution }) => {
  // Function to process the solution text and separate code blocks
  const processContent = (text: string): ContentPart[] => {
    const parts: ContentPart[] = [];
    let currentText = '';
    
    // Split by code blocks
    const blocks = text.split('```');
    
    blocks.forEach((block, index) => {
      if (index % 2 === 0) {
        // This is regular text
        if (block.trim()) {
          currentText += block;
        }
      } else {
        // This is a code block
        if (currentText) {
          parts.push({ type: 'text', content: currentText });
          currentText = '';
        }
        
        // Extract language if specified
        const lines = block.split('\n');
        const language = lines[0].trim() || 'python'; // Default to python if no language specified
        const code = lines.slice(1).join('\n');
        
        parts.push({ type: 'code', content: code, language });
      }
    });
    
    // Add any remaining text
    if (currentText) {
      parts.push({ type: 'text', content: currentText });
    }
    
    return parts;
  };

  // Format regular text (non-code) content
  const formatText = (text: string) => {
    // Format headers
    text = text.replace(/^### (.*$)/gm, '<h3>$1</h3>');
    text = text.replace(/^## (.*$)/gm, '<h2>$1</h2>');
    text = text.replace(/^# (.*$)/gm, '<h1>$1</h1>');
    
    // Format inline code
    text = text.replace(/`([^`]+)`/g, '<code>$1</code>');
    
    // Format lists - improved to handle more cases
    text = text.replace(/^\s*\* (.*)/gm, '<ul><li>$1</li></ul>');
    text = text.replace(/^\s*\d+\. (.*)/gm, '<ol><li>$1</li></ol>');
    
    // Format paragraphs
    text = text.replace(/^\s*(\n)?(.+)/gm, function(m) {
      return /\<(\/)?(h1|h2|h3|pre|code|ul|ol|li)/.test(m) ? m : '<p>' + m + '</p>';
    });
    
    return text;
  };

  const parts = processContent(solution);

  return (
    <div className="solution-container">
      <h2>Solution:</h2>
      <div className="solution-content">
        {parts.map((part, index) => {
          if (part.type === 'code') {
            return (
              <SyntaxHighlighter
                key={index}
                language={part.language}
                style={vscDarkPlus}
                showLineNumbers={true}
                wrapLines={true}
                customStyle={{
                  margin: '1em 0',
                  padding: '1em',
                  borderRadius: '4px',
                  fontSize: '14px',
                  backgroundColor: '#1e1e1e',
                  width: '100%'
                }}
              >
                {part.content}
              </SyntaxHighlighter>
            );
          } else {
            return (
              <div
                key={index}
                dangerouslySetInnerHTML={{ __html: formatText(part.content) }}
                className="text-content"
              />
            );
          }
        })}
      </div>
    </div>
  );
};

export default SolutionDisplay;
