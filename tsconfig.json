{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["dom", "es2020"], "jsx": "react", "strict": true, "esModuleInterop": true, "sourceMap": true, "outDir": "dist", "baseUrl": ".", "paths": {"*": ["node_modules/*"]}, "allowJs": true, "resolveJsonModule": true, "moduleResolution": "node", "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*"], "ts-node": {"transpileOnly": true}}